﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UniUtilities
{
    private static bool _TextureNameIDInitialized = false;
    private static int[] _TextureNameID;
    public static int[] s_TextureNameID
    {
        get
        {
            if (!_TextureNameIDInitialized)
            {
                _TextureNameID = new int[]
                    {
                        Shader.PropertyToID("_Tex0"),
                        Shader.PropertyToID("_Tex1"),
                        Shader.PropertyToID("_Tex2"),
                        Shader.PropertyToID("_Tex3"),
                        Shader.PropertyToID("_Tex4"),
                        Shader.PropertyToID("_Tex5"),
                        Shader.PropertyToID("_Tex6"),
                        Shader.PropertyToID("_Tex7"),
                    };
            }

            return _TextureNameID;
        }
    }

    private static int _LastTextureCount = 0;
    private static IntPtr[] _TextureHandles = null;
    private static Texture2D[] _Textures = null;
    public static Texture2D[] s_Textures
    {
        get
        {
            int newTextureCount = UniTextWrapper.GetTextureCount();
            if (newTextureCount != _LastTextureCount)
            {
                // destroy old textures;
                if (_Textures != null)
                {
                    for (int i = 0, iMax = _Textures.Length; i < iMax; i++)
                    {
                        SafeDestroy(_Textures[i]);
                        _Textures[i] = null;
                    }
                }

                _Textures = new Texture2D[newTextureCount];
                _TextureHandles = new IntPtr[newTextureCount];
                _LastTextureCount = newTextureCount;
            }

            for (int i = 0; i < newTextureCount; i++)
            {
                IntPtr ptr = UniTextWrapper.GetTextureHandle(i);
                if (ptr != _TextureHandles[i])
                {
                    SafeDestroy(_Textures[i]);
                    _Textures[i] = null;
                    _TextureHandles[i] = ptr;
                }

                if (ptr != IntPtr.Zero)
                {
                    if (_Textures[i] == null)
                    {
                        _Textures[i] = Texture2D.CreateExternalTexture(2048, 2048, TextureFormat.Alpha8, false, false, ptr);
                        //https://docs.unity3d.com/Manual/SL-SamplerStates.html
                        _Textures[i].filterMode = FilterMode.Point;
                        _Textures[i].filterMode = FilterMode.Bilinear;
                        _Textures[i].wrapMode = TextureWrapMode.Repeat;
                        _Textures[i].name = "UniFont_Atlas_" + i;
                    }
                }
            }

            return _Textures;
        }
    }

    static void SafeDestroy(UnityEngine.Object obj)
    {
        if (obj != null)
        {
            #if UNITY_EDITOR
            if (Application.isPlaying)
            {
                UnityEngine.Object.Destroy(obj);
            }
            else
            {
                UnityEngine.Object.DestroyImmediate(obj);
            }
            #else
            UnityEngine.Object.Destroy(obj);
            #endif
        }
    }
}