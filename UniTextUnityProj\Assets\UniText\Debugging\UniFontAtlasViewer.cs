using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[ExecuteAlways]
public class UniFontAtlasViewer : MonoBehaviour
{
    public Vector4 atlasRect;
    public int atlasIndex = 0;

    /// <summary>
    /// Used to display the atlas texture in the UI.
    /// </summary>
    public RawImage rawImage;

    void UpdateRawImage()
    {
        if (rawImage != null && UniUtilities.s_Textures != null && atlasIndex < UniUtilities.s_Textures.Length)
        {
            rawImage.texture = UniUtilities.s_Textures[atlasIndex];
            rawImage.uvRect = new Rect(atlasRect.x, atlasRect.y, atlasRect.z, atlasRect.w);
        }
    }

#if UNITY_EDITOR
    void OnValidate()
    {
        UpdateRawImage();
    }
#endif
}
