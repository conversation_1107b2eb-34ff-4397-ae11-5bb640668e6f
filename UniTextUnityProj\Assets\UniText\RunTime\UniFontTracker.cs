using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UniFontTracker
{
    private static HashSet<UniFont> s_Fonts = new HashSet<UniFont>();

    public static void Add(UniFont uniFont)
    {
        s_Fonts.Add(uniFont);
    }

    public static void Remove(UniFont uniFont)
    {
        s_Fonts.Remove(uniFont);
    }

    public static void UpdateUnused()
    {
        foreach(var font in s_Fonts)
        {
            //UniTextWrapper.UnloadUnusedGlyphs(font.GetNativePtr());
        }
    }
}
