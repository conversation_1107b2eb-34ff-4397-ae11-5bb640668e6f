using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;
using System.Diagnostics;

public class BenchMark_04 : MonoBehaviour
{
    public TextAsset inputTextAsset;
    public UniText_UGUI uniText;

    // Start is called before the first frame update
    void Start()
    {
        // preparing
        string inputText = inputTextAsset.text;
        //uniText.fontPath = "Assets/Fonts/Font_FounderZZ.ttf";
        uniText.fontSize = 28;

        //var fontPtr = UniTextWrapper.LoadFontFromPath(uniText.fontPath);
        //UniTextWrapper.SetRenderMode(fontPtr, RenderMode.Smooth);
        uniText.font.UnsafeCallToClearGlyphs();

        Stopwatch sw = new Stopwatch();
        sw.Start();
        uniText.text = inputText;
        Canvas.ForceUpdateCanvases();
        sw.Stop();
        UnityEngine.Debug.LogFormat("Benchmark_04 - UniText Text.setTextAndRebuild : time elapsed = {0}ms", sw.ElapsedMilliseconds);
    }
}

