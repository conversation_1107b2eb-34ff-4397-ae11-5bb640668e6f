using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;

public class BenchMark_UniText : MonoBehaviour
{
    public int loopCount = 10000;
    public TextAsset inputTextAsset;
    
    public UnityEngine.UI.Text unityText;
    public TMPro.TextMeshProUGUI tmpText;
    public UniText_UGUI uniText;

    private IEnumerator BenchMark<T>(string inputText, T com, Action<T, string> textSetter)
    {
        UnityEngine.Profiling.Profiler.BeginSample("BenchMark: " + typeof(T).ToString());
        Stopwatch sw = new Stopwatch();
        sw.Start();
        for (int i = 0; i < loopCount; i++)
        {
            textSetter(com, inputText);
            Canvas.ForceUpdateCanvases();
        }
        sw.Stop();
        UnityEngine.Debug.LogFormat("Benchmark_01 - {0} : time elapsed = {1}ms", typeof(T).ToString(), sw.ElapsedMilliseconds);
        UnityEngine.Profiling.Profiler.EndSample();

        yield return null;
    }

    private void SetText(UnityEngine.UI.Text com, string text)
    {
        com.text = text;
    }

    private void SetText(TMPro.TextMeshProUGUI com, string text)
    {
        com.text = text;
    }

    private void SetText(UniText_UGUI com, string text)
    {
        com.text = text;
    }

    private IEnumerator BenchMarkEntry(string inputText)
    {
        yield return BenchMark<UnityEngine.UI.Text>(inputText, unityText, SetText);

        yield return null;
        yield return BenchMark<TMPro.TextMeshProUGUI>(inputText, tmpText, SetText);

        yield return null;
        yield return BenchMark<UniText_UGUI>(inputText, uniText, SetText);
    }

    public void Start()
    {
        //uniText.fontPath = "Assets/Fonts/PFAgoraSlabPro Bold.ttf";
        Canvas.ForceUpdateCanvases();

        string inputText = inputTextAsset.text;
        StartCoroutine(BenchMarkEntry(inputText));
    }

}
