// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "UI/Default Smooth Font" {
	Properties{
		_Tex0("Font Texture 0", 2D) = "white" {}
		_Tex1("Font Texture 1", 2D) = "white" {}
		_Tex2("Font Texture 2", 2D) = "white" {}
		_Tex3("Font Texture 3", 2D) = "white" {}
		_Tex4("Font Texture 4", 2D) = "white" {}
		_Tex5("Font Texture 5", 2D) = "white" {}
		_Tex6("Font Texture 6", 2D) = "white" {}
		_Tex7("Font Texture 7", 2D) = "white" {}

		_Color("Tint", Color) = (1,1,1,1)

		_Width("Width", Float) = 0.5
		_Edge("Edge", Float) = 0.1
		_StencilComp("Stencil Comparison", Float) = 8
		_Stencil("Stencil ID", Float) = 0
		_StencilOp("Stencil Operation", Float) = 0
		_StencilWriteMask("Stencil Write Mask", Float) = 255
		_StencilReadMask("Stencil Read Mask", Float) = 255

		_ColorMask("Color Mask", Float) = 15

		[Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip("Use Alpha Clip", Float) = 0
	}

		SubShader
		{
			Tags
			{
				"Queue" = "Transparent"
				"IgnoreProjector" = "True"
				"RenderType" = "Transparent"
				"PreviewType" = "Plane"
				"CanUseSpriteAtlas" = "True"
			}

			Stencil
			{
				Ref[_Stencil]
				Comp[_StencilComp]
				Pass[_StencilOp]
				ReadMask[_StencilReadMask]
				WriteMask[_StencilWriteMask]
			}

			Cull Off
			Lighting Off
			ZWrite Off
			ZTest[unity_GUIZTestMode]
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
			ColorMask[_ColorMask]

			Pass
			{
				Name "Default"
				Tags { "LightMode" = "ForwardBase" }
			CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				#pragma target 2.0

				#include "UnityCG.cginc"
				#include "UnityUI.cginc"

				#pragma multi_compile __ UNITY_UI_ALPHACLIP SOFT_CLIP 

				struct appdata_t
				{
					float4 vertex   : POSITION;
					float4 color    : COLOR;
					float2 texcoord : TEXCOORD0;
					float2 texcoord1 : TEXCOORD1;
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				struct v2f
				{
					float4 vertex   : SV_POSITION;
					float4 color : COLOR;
					float2 texcoord  : TEXCOORD0;
					float4 worldPosition : TEXCOORD1;
					float2 texcoord1 : TEXCOORD2;
					UNITY_VERTEX_OUTPUT_STEREO
				};

				float4 _Color;
				float4 _TextureSampleAdd;
				float4 _ClipRect;

				float _Width;
				float _Edge;

				#ifdef SOFT_CLIP
				half4 _BorderBlend;
				float4 _BorderBlendAlpha;
				#endif


				v2f vert(appdata_t IN)
				{
					v2f OUT;
					UNITY_SETUP_INSTANCE_ID(IN);
					UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
					OUT.worldPosition = IN.vertex;
					OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);

					OUT.texcoord = IN.texcoord;
					OUT.texcoord1 = IN.texcoord1;

					OUT.color = IN.color * _Color;
					return OUT;
				}

				sampler2D _Tex0;
				sampler2D _Tex1;
				sampler2D _Tex2;
				sampler2D _Tex3;
				sampler2D _Tex4;
				sampler2D _Tex5;
				sampler2D _Tex6;
				sampler2D _Tex7;

				inline float GetSoft2DClipping(in float2 position, in float4 clipRect , in half4 borderBlend)
				{
					//float2 range = max(0, float2(horizontal , vertical));
					float2 inside = smoothstep(clipRect.xy , clipRect.xy + borderBlend.xy, position.xy) * smoothstep(clipRect.zw , clipRect.zw - borderBlend.zw, position.xy);
					return inside.x * inside.y;
			    }

				fixed4 frag(v2f IN) : SV_Target
				{
					half4 color;
					color.rgb = IN.color.rgb;
					if (IN.texcoord1.x == 0)
					{
						color.a = tex2D(_Tex0, IN.texcoord.xy).a * IN.color.a;
					}
					else if (IN.texcoord1.x == 1)
					{
						color.a = tex2D(_Tex1, IN.texcoord.xy).a * IN.color.a;
					}
					else if (IN.texcoord1.x == 2)
					{
						color.a = tex2D(_Tex2, IN.texcoord.xy).a * IN.color.a;
					}
					else if (IN.texcoord1.x == 3)
					{
						color.a = tex2D(_Tex3, IN.texcoord.xy).a * IN.color.a;
					}
					else if (IN.texcoord1.x == 4)
					{
						color = (tex2D(_Tex4, IN.texcoord.xy) + _TextureSampleAdd) * IN.color;
					}
					else if (IN.texcoord1.x == 5)
					{
						color = (tex2D(_Tex5, IN.texcoord.xy) + _TextureSampleAdd) * IN.color;
					}
					else if (IN.texcoord1.x == 6)
					{
						color = (tex2D(_Tex6, IN.texcoord.xy) + _TextureSampleAdd) * IN.color;
					}
					else if (IN.texcoord1.x == 7)
					{
						color = (tex2D(_Tex7, IN.texcoord.xy) + _TextureSampleAdd) * IN.color;
					}
					
					//float distance = 1.0 - color.a;
				    //color.a = 1.0 - smoothstep(_Width, _Width + _Edge, distance);
					
#ifdef UNITY_UI_ALPHACLIP
                    clip(color.a - 0.001);
#endif
				    return color;
			    }
			ENDCG
			}
		}
}
