﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEditor;
using UnityEngine;

[CreateAssetMenu(menuName = "UniTextBridge/Font")]
[Serializable]
public partial class UniFont : ScriptableObject
{
    //private bool m_Initialized = false;

    [SerializeField]
    private int m_FontSize = 16;
    public int fontSize
    {
        get { return m_FontSize; }
        set { if (value != m_FontSize) { m_FontSize = value; } }
    }

    [SerializeField]
    private int m_MinFontSize = 14;
    [SerializeField]
    private int m_MaxFontSize = 72;

    [SerializeField]
    private byte m_Padding = 1;
    public byte padding
    {
        get { return m_Padding; }
        set { if (value != m_Padding) { m_Padding = value; } }
    }
    /// <summary>
    /// TextMeshPro use UnityEngine.Font as input, but that's gonna take additional memory usage.
    /// Here we only need the path of that font, and then we can cache the font face in c++.
    /// TODO:
    /// Make this read-only
    /// </summary> 
    [SerializeField]
    private string m_FontPath;
    public string fontPath
    {
        get { return m_FontPath; }
        //set { if (value != m_FontPath) { m_FontPath = value; } }
    }

    [SerializeField]
    private PackingMethod m_PackingMethod = PackingMethod.Shelf;
    public PackingMethod packingMethod
    {
        get { return m_PackingMethod; }
        set { if (value != m_PackingMethod) { m_PackingMethod = value; } }
    }

    [SerializeField]
    private RenderMode m_RenderMode;
    public RenderMode renderMode 
    {
        get { return m_RenderMode; }
        set { if (value != m_RenderMode) { m_RenderMode = value; } }
    }

    /// <summary>
    /// Reference checking, for texture releasing
    /// </summary>
    protected List<WeakReference<UniText_UGUI>> m_References = new List<WeakReference<UniText_UGUI>>();

    public void RegisterUniText(UniText_UGUI text)
    {
#if ENABLE_UniTextBridge_CHECK
        if (IsTextAlreadyReferenced(text)) return;
#endif
        // maybe we should pool this WeakReference Object instead of creating a new instance 
        m_References.Add(new WeakReference<UniText_UGUI>(text));
    }

    public void UnregisterUniText(UniText_UGUI text)
    {
        for (int i = m_References.Count - 1; i >= 0; i--)
        {
            var owner = m_References[i];
            UniText_UGUI target = null;
            if (owner.TryGetTarget(out target) && target == text)
            {
                m_References.RemoveAt(i);
                break;
            }
        }
    }

    private IntPtr m_BufferPtr;
    private IntPtr m_NativePtr;
    public IntPtr GetNativePtr() { TryInitialize(); return m_NativePtr; }
    private void Awake()
    {
        TryInitialize();
        UniFontTracker.Add(this);
    }

    private void OnDestroy()
    {
        UniFontTracker.Remove(this);
        if (m_BufferPtr != IntPtr.Zero)
        {
            Marshal.FreeHGlobal(m_BufferPtr);
        }
    }

#if ENABLE_UniTextBridge_CHECK
    private bool IsTextAlreadyReferenced(UniText_UGUI text)
    {
        for (int i = 0, iMax = m_References.Count; i < iMax; i++)
        {
            var owner = m_References[i];
            UniText_UGUI target = null;
            if (owner.TryGetTarget(out target) && target == text)
            {
                return true;
            }
        }

        return false;
    }
#endif

    private void ApplyNativeFontProperties()
    {
        if (m_NativePtr != IntPtr.Zero)
        {
            UniTextWrapper.SetPackingMothod(m_NativePtr, packingMethod);
            UniTextWrapper.SetPadding(m_NativePtr, padding);
            UniTextWrapper.SetRenderMode(m_NativePtr, renderMode);
        }
    }

    private void ReloadFont()
    {
        if (m_BufferPtr != IntPtr.Zero)
        {
            Marshal.FreeHGlobal(m_BufferPtr);
        }

        if (!string.IsNullOrEmpty(fontPath))
        {
            byte[] bytes = null;
            bytes = System.IO.File.ReadAllBytes(fontPath);

            int size = Marshal.SizeOf(bytes[0]) * bytes.Length;
            m_BufferPtr = Marshal.AllocHGlobal(size);
            Marshal.Copy(bytes, 0, m_BufferPtr, bytes.Length);
            
            m_NativePtr = UniTextWrapper.LoadFontFromMemory(fontPath + this.GetInstanceID(), m_BufferPtr, (ulong)size); 
            ApplyNativeFontProperties();
        }
        //m_Initialized = m_NativePtr != IntPtr.Zero;
    }

    public void TryInitialize()
    {
        if (m_NativePtr != IntPtr.Zero) return;

        ReloadFont();
    }

    public void UnsafeCallToClearGlyphs()
    {
        if (m_NativePtr != IntPtr.Zero)
        {
            UniTextWrapper.ClearGlyphs(m_NativePtr);
        }
    }

    public void ApplyPropertyChanges()
    {
        ApplyNativeFontProperties();

        if (m_NativePtr != IntPtr.Zero)
        {
            UniTextWrapper.ClearGlyphs(m_NativePtr);
            
            // force a rebuild
            for (int i = 0, iMax = m_References.Count; i < iMax; i++)
            {
                var owner = m_References[i];
                UniText_UGUI target = null;
                if (owner.TryGetTarget(out target))
                {
                    target.SetAllDirty();
                }
            }

            Canvas.ForceUpdateCanvases();
        }
    }
}
