﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.InteropServices;
using UnityEngine;

[StructLayout(LayoutKind.Sequential)]
public struct UniVertex2D
{
    public Vector2 position;
    public Vector4 uv0;
    public Color32 color;
}

public enum PackingMethod : byte
{
    Grid = 0,
    MaxR<PERSON><PERSON>,
    Shelf,
    // not supported
    //Skyline,
    //Guillotine
};

public enum RenderMode : byte
{
    Smooth,
    SmoothHinted,
    Raster,
    SDF,        // SDF(from outline to sdf),
    BSDF,       // BSDF(from bitmap to sdf),
    CSDF,       // CSDF(Custom SDF Generation)
    VSDF,       // VSDF(Vector SDF)
};

public enum HorizontalAlignment : byte
{
    Left, Center, Right
};

public enum VerticalAlignment : byte
{
    Top, Middle, Bottom
};

public enum TextOverflow : byte
{
    Wrap, Overflow, Truncate, Ellipse
};

public enum HorizontalOverflow : byte
{
    Overflow = TextOverflow.Overflow,
    Wrap = TextOverflow.Wrap,
    Truncate = TextOverflow.Truncate,
    Ellipse = TextOverflow.Ellipse,
}

public enum VerticalOverflow : byte
{
    Overflow = TextOverflow.Overflow,
    Truncate = TextOverflow.Truncate,
    Ellipse = TextOverflow.Ellipse,
}

public enum LogLevel : int
{
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3
}

public enum FontStyle : byte
{
    Regular = 0,
    Bold = 1,
    Italic = 2,
    BoldItalic = 3
}

public static class UniTextWrapper
{
    // Logging delegate that matches the native LogFunction signature
    public delegate void LogDelegate(int level, string message);

    // Keep a reference to prevent garbage collection
    private static LogDelegate s_logDelegate;

    static UniTextWrapper()
    {
        Canvas.willRenderCanvases += PostGraphicRebuild;

        // Initialize Unity logging
        InitializeUnityLogging();
    }

    static void PostGraphicRebuild()
    {
        //ApplyFontAtlases();
        UniFontTracker.UpdateUnused();
    }

    /// <summary>
    /// Initialize Unity logging integration
    /// </summary>
    private static void InitializeUnityLogging()
    {
        // Create the logging delegate
        s_logDelegate = UnityLogCallback;

        // Convert delegate to function pointer and set it
        IntPtr logFunctionPtr = Marshal.GetFunctionPointerForDelegate(s_logDelegate);
        SetUnityLogHandler(logFunctionPtr);
    }

    /// <summary>
    /// Unity logging callback that uses Debug.Log with appropriate log levels
    /// </summary>
    /// <param name="level">Log level from LogLevel enum</param>
    /// <param name="message">Log message</param>
    private static void UnityLogCallback(int level, string message)
    {
        LogLevel logLevel = (LogLevel)level;
        switch (logLevel)
        {
            case LogLevel.Debug:
                Debug.Log($"[UniText Debug] {message}");
                break;
            case LogLevel.Info:
                Debug.Log($"[UniText] {message}");
                break;
            case LogLevel.Warning:
                Debug.LogWarning($"[UniText] {message}");
                break;
            case LogLevel.Error:
                Debug.LogError($"[UniText] {message}");
                break;
            default:
                Debug.Log($"[UniText] {message}");
                break;
        }
    }

    const string dll = "libUniText";

    #region UniTextGenerator.h
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern IntPtr CreateUniText();

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void DestroyUniText(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern void SetFont(IntPtr ptr, string fontPath);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetFont(IntPtr ptr, IntPtr fontPtr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetFontSize(IntPtr ptr, int fontSize);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern int GetFontSize(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern void SetText(IntPtr ptr, string text);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetExtents(IntPtr ptr, float width, float height);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetPivot(IntPtr ptr, float x, float y);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetActive(IntPtr ptr, bool active);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetColor(IntPtr ptr, float r, float g, float b, float a);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetStrokeSize(IntPtr ptr, float strokeSize);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetFontStyle(IntPtr ptr, int fontStyle);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetAutoSizeRange(IntPtr ptr, int minFontSize, int maxFontSize);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void EnableRichText(IntPtr ptr, bool enabled);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void EnableKerning(IntPtr ptr, bool enabled);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void EnableAutoSize(IntPtr ptr, bool enabled);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void EnableRTL(IntPtr ptr, bool enabled);
    
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern void AppendText(IntPtr ptr, string text);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetHorizontalAlignment(IntPtr ptr, byte horizontalAlignment);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern byte GetHorizontalAlignment(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetVerticalAlignment(IntPtr ptr, byte verticalAlignment);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern byte GetVerticalAlignment(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetHorizontalOverflow(IntPtr ptr, byte horizontalOverflow);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern byte GetHorizontalOverflow(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetVerticalOverflow(IntPtr ptr, byte verticalOverflow);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern byte GetVerticalOverflow(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetCharacterSpacing(IntPtr ptr, float spacing);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern float GetCharacterSpacing(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetLineSpacing(IntPtr ptr, float spacing);
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern float GetLineSpacing(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void GetColor(IntPtr ptr, out float r, out float g, out float b, out float a);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern float GetStrokeSize(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void GetPivot(IntPtr ptr, out float pivot_x, out float pivot_y);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern int GetFontStyle(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void GetAutoSizeRange(IntPtr ptr, out int minFontSize, out int maxFontSize);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern bool HasRichText(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern bool HasKerning(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern bool HasAutoSize(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern bool HasRTL(IntPtr ptr);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern int GetVertexCount(IntPtr ptr);

    // https://learn.microsoft.com/en-us/dotnet/framework/interop/marshalling-different-types-of-arrays
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void FillVertices(IntPtr ptr, [In, Out] UniVertex2D[] vertexArray, int startIndex, int vertexCount);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void FillVerticesPerChannel(IntPtr ptr, [In, Out] Vector2[] posArray, [In, Out] Vector4[] uvArray, [In, Out] Color32[] colorArray, int startIndex, int vertexCount);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern int GetTextureCount();

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern IntPtr GetTextureHandle(int textureIndex);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void ApplyFontAtlases();

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void Rebuild(IntPtr ptr);
    #endregion

    #region UniFont.h
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern IntPtr GetFont(string fontPath);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern IntPtr LoadFontFromPath(string fontPath);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern IntPtr LoadFontFromMemory(string fontPath, IntPtr fontBuffer, ulong bufferSize);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetPackingMothod(IntPtr uniFont, PackingMethod packingMethod);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetPadding(IntPtr uniFont, byte padding);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetRenderMode(IntPtr uniFont, RenderMode renderMode);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern void AddFallbackFont(IntPtr uniFont, string fontName);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern IntPtr GetFamilyName(IntPtr uniFont);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern int GetStyle(IntPtr uniFont);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern int GetFontGUID(IntPtr uniFont);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void GetFontSizeRange(IntPtr uniFont, out int minSize, out int maxSize);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true, CharSet = CharSet.Ansi)]
    public static extern void TryPackGlyphs(IntPtr uniFont, string text, int fontSize);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void UnloadUnusedGlyphs(IntPtr uniFont);

    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void ClearGlyphs(IntPtr uniFont);
    #endregion

    #region Logging
    [DllImport(dll, CallingConvention = CallingConvention.Cdecl, ExactSpelling = true)]
    public static extern void SetUnityLogHandler(IntPtr logHandler);
    #endregion

    #region Helper Methods
    /// <summary>
    /// Helper method to get font family name as string
    /// </summary>
    public static string GetFamilyNameString(IntPtr uniFont)
    {
        IntPtr namePtr = GetFamilyName(uniFont);
        if (namePtr != IntPtr.Zero)
        {
            return Marshal.PtrToStringAnsi(namePtr);
        }
        return string.Empty;
    }
    #endregion

}
