using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;
using System.Diagnostics;

public class BenchMark_02 : MonoBehaviour
{
    public TextAsset inputTextAsset;
    public Text uguiText;

    // Start is called before the first frame update
    void Start()
    {
        // preparing
        string inputText = inputTextAsset.text;

        Stopwatch sw = new Stopwatch();
        sw.Start();
        uguiText.text = inputText;
        Canvas.ForceUpdateCanvases();
        sw.Stop();
        UnityEngine.Debug.LogFormat("Benchmark_02 - UGUI Text.setTextAndRebuild : time elapsed = {0}ms", sw.ElapsedMilliseconds);
    }
}

