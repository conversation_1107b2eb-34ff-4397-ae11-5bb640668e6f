Using pre-set license
Built from '2022.3/release' branch; Version is '2022.3.61f1 (6c53ebaf375d) revision 7099371'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 65205 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.61f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/trunk_base/Young/UniTextGit/UniTextUnityProj
-logFile
Logs/AssetImportWorker0.log
-srvPort
3349
Successfully changed project path to: E:/trunk_base/Young/UniTextGit/UniTextUnityProj
E:/trunk_base/Young/UniTextGit/UniTextUnityProj
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [43552]  Target information:

Player connection [43552]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1599054819 [EditorId] 1599054819 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43552]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1599054819 [EditorId] 1599054819 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [43552] Host joined multi-casting on [***********:54997]...
Player connection [43552] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
Refreshing native plugins compatible for Editor in 1.01 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/trunk_base/Young/UniTextGit/UniTextUnityProj/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2882)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56036
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005158 seconds.
- Loaded All Assemblies, in  0.220 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2013 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.223 seconds
Domain Reload Profiling: 2442ms
	BeginReloadAssembly (71ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (86ms)
		LoadAssemblies (69ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (85ms)
			TypeCache.Refresh (84ms)
				TypeCache.ScanAssembly (75ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2223ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2194ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2062ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (90ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.329 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.329 seconds
Domain Reload Profiling: 657ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (15ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (160ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (33ms)
				TypeCache.ScanAssembly (25ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (329ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (218ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (47ms)
			ProcessInitializeOnLoadAttributes (124ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.30 seconds
Refreshing native plugins compatible for Editor in 0.59 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1271 Unused Serialized files (Serialized files now loaded: 0)
Unloading 14 unused Assets / (41.7 KB). Loaded Objects now: 1753.
Memory consumption went from 82.0 MB to 81.9 MB.
Total: 2.055400 ms (FindLiveObjects: 0.081500 ms CreateObjectMapping: 0.019500 ms MarkObjects: 1.887900 ms  DeleteObjects: 0.065300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Import Request.
  Time since last request: 26580.314683 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(f4c49d66992a26744822e11c53b167b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(f4c49d66992a26744822e11c53b167b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '80dbb5e563a952bbe42cbb1219ca108d') in 0.002109 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 181.872987 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat
  artifactKey: Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat using Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'ee24e231c52beedeb9ce2d6da1e98cbc') in 0.121603 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.001203 seconds.
  path: Assets/UniText/Editor
  artifactKey: Guid(1a3ab47f74a8b3e4f8460ffbb5180e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Editor using Guid(1a3ab47f74a8b3e4f8460ffbb5180e25) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '2995fe993ce0b77373fe0dc600761eaf') in 0.000307 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001515 seconds.
  path: Assets/TextMesh Pro/Documentation
  artifactKey: Guid(8e7e8f5a82a3a134e91c54efd2274ea9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Documentation using Guid(8e7e8f5a82a3a134e91c54efd2274ea9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '097ee97484745b541abb5aabd54e2ae1') in 0.000248 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001896 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMPro.cginc
  artifactKey: Guid(407bc68d299748449bbf7f48ee690f8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMPro.cginc using Guid(407bc68d299748449bbf7f48ee690f8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '6f2245c1d54272e4afcc28e733c123f3') in 0.000564 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.002056 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF.shader
  artifactKey: Guid(68e6db2ebdc24f95958faec2be5558d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF.shader using Guid(68e6db2ebdc24f95958faec2be5558d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'fde4f0e9026f64895c9231ed433d421b') in 0.000376 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.009935 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMPro_Surface.cginc
  artifactKey: Guid(d930090c0cd643c7b55f19a38538c162) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMPro_Surface.cginc using Guid(d930090c0cd643c7b55f19a38538c162) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'e7d3378ec2d58f0caba549f3f72511b7') in 0.000539 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.009127 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans - OFL.txt
  artifactKey: Guid(6e59c59b81ab47f9b6ec5781fa725d2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans - OFL.txt using Guid(6e59c59b81ab47f9b6ec5781fa725d2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'a4adbfa72b56579610a56d86d09b2249') in 0.000476 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001013 seconds.
  path: Assets/TextMesh Pro/Resources/Style Sheets
  artifactKey: Guid(4aecb92fff08436c8303b10eab8da368) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Style Sheets using Guid(4aecb92fff08436c8303b10eab8da368) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '7177ed8b9e64d6f961a4dba0965c9388') in 0.000205 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.002269 seconds.
  path: Assets/UniText/Plugins/Windows/x64/libUniText.dll
  artifactKey: Guid(516a25c6a9cb29b44a6ed4f926b4c841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Plugins/Windows/x64/libUniText.dll using Guid(516a25c6a9cb29b44a6ed4f926b4c841) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'dc4867bde03a9791cd48ef1d04b97097') in 0.000276 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.015879 seconds.
  path: Assets/UniText/RunTime/UniText_UGUI.cs
  artifactKey: Guid(5ebd99248cbf07a4aa241cefde1dfcc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/RunTime/UniText_UGUI.cs using Guid(5ebd99248cbf07a4aa241cefde1dfcc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '0a58b266cb0b77d56dcc33c68f9f96bd') in 0.000336 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.006431 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Mobile Masking.shader
  artifactKey: Guid(bc1ede39bf3643ee8e493720e4259791) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Mobile Masking.shader using Guid(bc1ede39bf3643ee8e493720e4259791) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '6b69e51216f0698375ed87a55252bd98') in 0.000451 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.002574 seconds.
  path: Assets/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset
  artifactKey: Guid(c41005c129ba4d66911b75229fd70b45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset using Guid(c41005c129ba4d66911b75229fd70b45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '3fa93cae0637633898c34f39384eef60') in 0.002539 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.001767 seconds.
  path: Assets/UniText/RunTime/UniFontTracker.cs
  artifactKey: Guid(6db6fb2cf8adf32419eb7d698bec7a21) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/RunTime/UniFontTracker.cs using Guid(6db6fb2cf8adf32419eb7d698bec7a21) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '0c5a520cfe1bc7bf2ee0515a0f5696f4') in 0.000288 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.002017 seconds.
  path: Assets/UniText/Example/UI_DefaultSDFFont.shader
  artifactKey: Guid(7e18dbebc3a083f4aa4879a8333abb8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Example/UI_DefaultSDFFont.shader using Guid(7e18dbebc3a083f4aa4879a8333abb8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '7a0491fd37e6f71bf44b6c9d91ed686d') in 0.000432 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001860 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Mobile.shader
  artifactKey: Guid(fe393ace9b354375a9cb14cdbbc28be4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Mobile.shader using Guid(fe393ace9b354375a9cb14cdbbc28be4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'cf8bda167642c082a35dabb62623565f') in 0.000335 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.014493 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_Sprite.shader
  artifactKey: Guid(cf81c85f95fe47e1a27f6ae460cf182c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_Sprite.shader using Guid(cf81c85f95fe47e1a27f6ae460cf182c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '334615dcf211106b3449cbddcd9c9f56') in 0.000460 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.002676 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt
  artifactKey: Guid(381dcb09d5029d14897e55f98031fca5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt using Guid(381dcb09d5029d14897e55f98031fca5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '8919eebe9d99d6decd1683eed81e7b64') in 0.000530 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001422 seconds.
  path: Assets/UniText/RunTime/UniTextNativeAPI.cs
  artifactKey: Guid(1ca1b44e0a44a99448356ed708205754) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/RunTime/UniTextNativeAPI.cs using Guid(1ca1b44e0a44a99448356ed708205754) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '362d3fb4101490415cf9af456113a160') in 0.000184 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.022295 seconds.
  path: Assets/Scenes/SampleSceneSettings.lighting
  artifactKey: Guid(462c0ea52e165864c958bf012e9ba961) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleSceneSettings.lighting using Guid(462c0ea52e165864c958bf012e9ba961) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '8a0a3cf3b9764a588a1850325bb2cc8d') in 0.000700 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001300 seconds.
  path: Assets/UniText/Example
  artifactKey: Guid(f534f83754bb5584394014447d631659) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Example using Guid(f534f83754bb5584394014447d631659) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '18547ffee6635efa0cbddea64490783c') in 0.000283 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001273 seconds.
  path: Assets/Fonts
  artifactKey: Guid(9fc69a1f89b568b4fb6bc17601eee7b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Fonts using Guid(9fc69a1f89b568b4fb6bc17601eee7b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '01a3696b02bb93c760e5f856d86ae9c8') in 0.000195 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001817 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_Bitmap.shader
  artifactKey: Guid(128e987d567d4e2c824d754223b3f3b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_Bitmap.shader using Guid(128e987d567d4e2c824d754223b3f3b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'a105a03ad1eadfcfa4ea2924737837fd') in 0.000342 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001926 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat
  artifactKey: Guid(79459efec17a4d00a321bdcc27bbc385) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat using Guid(79459efec17a4d00a321bdcc27bbc385) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '636834daf92432ac7a5277db37ecb9db') in 0.041910 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.002711 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Surface.shader
  artifactKey: Guid(f7ada0af4f174f0694ca6a487b8f543d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Surface.shader using Guid(f7ada0af4f174f0694ca6a487b8f543d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '58f5e5e86480b6869bd700f9a40c8c38') in 0.000599 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001547 seconds.
  path: Assets/TextMesh Pro/Resources/Sprite Assets
  artifactKey: Guid(512a49d95c0c4332bdd98131869c23c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Sprite Assets using Guid(512a49d95c0c4332bdd98131869c23c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'c9022e1130fa0f6a3c30e29e54e09e73') in 0.000272 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.011709 seconds.
  path: Assets/UniText/Editor/UniTextMeshEditor.cs
  artifactKey: Guid(7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Editor/UniTextMeshEditor.cs using Guid(7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '4d335dc0f236026e7fe824d7becbf3b0') in 0.000398 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001497 seconds.
  path: Assets/Scenes
  artifactKey: Guid(7b218bfc8a66ba546858a49b1b3fb9e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes using Guid(7b218bfc8a66ba546858a49b1b3fb9e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'd0dee4281b1906ec1f557ebcf3871e1d') in 0.000394 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.017017 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders
  artifactKey: Guid(99f836c9cb9345dba2e72c4a1f2d0695) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders using Guid(99f836c9cb9345dba2e72c4a1f2d0695) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '7cb3e9a1a6666164c5680cdd863d961d') in 0.000330 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.003199 seconds.
  path: Assets/TextMesh Pro/Resources/LineBreaking Following Characters.txt
  artifactKey: Guid(fade42e8bc714b018fac513c043d323b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/LineBreaking Following Characters.txt using Guid(fade42e8bc714b018fac513c043d323b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '24cf00695fcba3beea73c5c877391637') in 0.000786 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001407 seconds.
  path: Assets/TextMesh Pro/Fonts
  artifactKey: Guid(6ab70aee4d56447429c680537fbf93ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts using Guid(6ab70aee4d56447429c680537fbf93ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '20111567da1f29bfd88bf993dacb0055') in 0.000256 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001915 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF Overlay.shader
  artifactKey: Guid(dd89cf5b9246416f84610a006f916af7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF Overlay.shader using Guid(dd89cf5b9246416f84610a006f916af7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '744c4cb08f661d9461e3df33cef1f1c3') in 0.000345 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001979 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.json
  artifactKey: Guid(8f05276190cf498a8153f6cbe761d4e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.json using Guid(8f05276190cf498a8153f6cbe761d4e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'df0a471d95dedb7df1c55a8c8620fe9c') in 0.000574 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.010215 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.png
  artifactKey: Guid(dffef66376be4fa480fb02b19edbe903) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.png using Guid(dffef66376be4fa480fb02b19edbe903) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'ea096258abfa9fe8fba8ff63d3c2ae50') in 0.011638 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.002131 seconds.
  path: Assets/UniText/Plugins/Windows
  artifactKey: Guid(0bb0b0d6550f41a4daecfd039f4e62d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Plugins/Windows using Guid(0bb0b0d6550f41a4daecfd039f4e62d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '713487a977707f427556cd77d1532081') in 0.000339 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001384 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials
  artifactKey: Guid(731f1baa9d144a9897cb1d341c2092b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials using Guid(731f1baa9d144a9897cb1d341c2092b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '4c6deede9e0caf520236954822387b13') in 0.000248 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001831 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(f4c49d66992a26744822e11c53b167b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(f4c49d66992a26744822e11c53b167b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '3018bec730e977a1b44ddad931890402') in 0.000224 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001317 seconds.
  path: Assets/TextMesh Pro
  artifactKey: Guid(f54d1bd14bd3ca042bd867b519fee8cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro using Guid(f54d1bd14bd3ca042bd867b519fee8cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'dd6ad478d9966b0e483c01b5bd727387') in 0.000226 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.014846 seconds.
  path: Assets/UniText/Editor/UniFontEditor.cs
  artifactKey: Guid(89fd1748f49298640ad240e5cb8e7f6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/Editor/UniFontEditor.cs using Guid(89fd1748f49298640ad240e5cb8e7f6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'd723daed6b4d19ef261024218cbf23ee') in 0.000339 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001169 seconds.
  path: Assets/UniText
  artifactKey: Guid(0b138983840a2304b9d16afaf61d51ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText using Guid(0b138983840a2304b9d16afaf61d51ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '50fad55222268b0d71f821d019bd7988') in 0.000252 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001857 seconds.
  path: Assets/UniText/RunTime/UniText_Mesh.cs
  artifactKey: Guid(692fe18cf7caafb41abbbde55c7cf185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/RunTime/UniText_Mesh.cs using Guid(692fe18cf7caafb41abbbde55c7cf185) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '15fdcccdcc6b4782f02b63a04cd78717') in 0.000305 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.003670 seconds.
  path: Assets/TextMesh Pro/Sprites
  artifactKey: Guid(d0603b6d5186471b96c778c3949c7ce2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Sprites using Guid(d0603b6d5186471b96c778c3949c7ce2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '4efdb37c6eb1b7bda5571a62901eb1ba') in 0.000310 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.013450 seconds.
  path: Assets/TextMesh Pro/Documentation/TextMesh Pro User Guide 2016.pdf
  artifactKey: Guid(1b8d251f9af63b746bf2f7ffe00ebb9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Documentation/TextMesh Pro User Guide 2016.pdf using Guid(1b8d251f9af63b746bf2f7ffe00ebb9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '57a27060c29fba65670700a19a5b9cf1') in 0.000598 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.003056 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_Bitmap-Custom-Atlas.shader
  artifactKey: Guid(48bb5f55d8670e349b6e614913f9d910) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_Bitmap-Custom-Atlas.shader using Guid(48bb5f55d8670e349b6e614913f9d910) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'adcc703f57dfe2f7b23fd432cd130b6b') in 0.000385 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.003495 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Surface-Mobile.shader
  artifactKey: Guid(85187c2149c549c5b33f0cdb02836b17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Surface-Mobile.shader using Guid(85187c2149c549c5b33f0cdb02836b17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'cef54b3216ec5ba2834d5c3953640f68') in 0.000311 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.002054 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMPro_Properties.cginc
  artifactKey: Guid(3997e2241185407d80309a82f9148466) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMPro_Properties.cginc using Guid(3997e2241185407d80309a82f9148466) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'cf4dfd2a35c94415dfe4c5014bd82a33') in 0.000483 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.002264 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_Bitmap-Mobile.shader
  artifactKey: Guid(1e3b057af24249748ff873be7fafee47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_Bitmap-Mobile.shader using Guid(1e3b057af24249748ff873be7fafee47) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'd0995e203184008fa4ea13b989a00b5d') in 0.000397 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.003192 seconds.
  path: Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Mobile Overlay.shader
  artifactKey: Guid(a02a7d8c237544f1962732b55a9aebf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Shaders/TMP_SDF-Mobile Overlay.shader using Guid(a02a7d8c237544f1962732b55a9aebf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'cffaee5a1a7a8bccb57cefcc44db9b5a') in 0.000403 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.028198 seconds.
  path: Assets/TextMesh Pro/Resources/Style Sheets/Default Style Sheet.asset
  artifactKey: Guid(f952c082cb03451daed3ee968ac6c63e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Style Sheets/Default Style Sheet.asset using Guid(f952c082cb03451daed3ee968ac6c63e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '7a4a535f0c8369ad622695c42259c11d') in 0.001100 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.001732 seconds.
  path: Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt
  artifactKey: Guid(d82c1b31c7e74239bff1220585707d2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt using Guid(d82c1b31c7e74239bff1220585707d2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '7827a289b454398858d83064569f5a1a') in 0.000581 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.014762 seconds.
  path: Assets/UniText/RunTime/UniTextBridge.cs
  artifactKey: Guid(80258f2655460f94193100bc8e81d6d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniText/RunTime/UniTextBridge.cs using Guid(80258f2655460f94193100bc8e81d6d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'b4dafd63ec02ef7794e5d1e28962591b') in 0.000306 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.009187 seconds.
  path: Assets/TextMesh Pro/Fonts/LiberationSans.ttf
  artifactKey: Guid(e3265ab4bf004d28a9537516768c1c75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Fonts/LiberationSans.ttf using Guid(e3265ab4bf004d28a9537516768c1c75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: 'cc0b5300b8e0f983116f46e186ac1a94') in 0.008249 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 4
