Using pre-set license
Built from '2022.3/release' branch; Version is '2022.3.61f1 (6c53ebaf375d) revision 7099371'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 65205 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.61f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
E:/trunk_base/Young/UniTextGit/UniTextUnityProj
-logFile
Logs/AssetImportWorker1.log
-srvPort
2586
Successfully changed project path to: E:/trunk_base/Young/UniTextGit/UniTextUnityProj
E:/trunk_base/Young/UniTextGit/UniTextUnityProj
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [28664]  Target information:

Player connection [28664]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 689458755 [EditorId] 689458755 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28664]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 689458755 [EditorId] 689458755 [Version] 1048832 [Id] WindowsEditor(7,YOUNGXIANG-PC1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28664] Host joined multi-casting on [***********:54997]...
Player connection [28664] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
Refreshing native plugins compatible for Editor in 1.30 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/trunk_base/Young/UniTextGit/UniTextUnityProj/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4060 (ID=0x2882)
    Vendor:   NVIDIA
    VRAM:     7957 MB
    Driver:   32.0.15.6117
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56384
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.61f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.008827 seconds.
- Loaded All Assemblies, in  0.397 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 2139 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.324 seconds
Domain Reload Profiling: 2719ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (133ms)
		LoadAssemblies (154ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (128ms)
			TypeCache.Refresh (127ms)
				TypeCache.ScanAssembly (117ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (2324ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2296ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2189ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (73ms)
			ProcessInitializeOnLoadMethodAttributes (29ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.282 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.260 seconds
Domain Reload Profiling: 540ms
	BeginReloadAssembly (98ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (13ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (132ms)
		LoadAssemblies (161ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (32ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (19ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (261ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (176ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (101ms)
			ProcessInitializeOnLoadMethodAttributes (18ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Refreshing native plugins compatible for Editor in 0.56 ms, found 1 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1271 Unused Serialized files (Serialized files now loaded: 0)
Unloading 14 unused Assets / (41.7 KB). Loaded Objects now: 1753.
Memory consumption went from 82.0 MB to 82.0 MB.
Total: 1.848200 ms (FindLiveObjects: 0.079200 ms CreateObjectMapping: 0.017300 ms MarkObjects: 1.683400 ms  DeleteObjects: 0.067200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
========================================================================
Received Import Request.
  Time since last request: 86066.214531 seconds.
  path: Assets/Fonts/SuperPixel-m2L8j SDF.asset
  artifactKey: Guid(d83c3684d3380fd4a92bbc25841ce49d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Fonts/SuperPixel-m2L8j SDF.asset using Guid(d83c3684d3380fd4a92bbc25841ce49d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 24 workers.
 -> (artifact id: '2a5f1453b2d2033b10bfd4b41cd91d55') in 0.073948 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 6
