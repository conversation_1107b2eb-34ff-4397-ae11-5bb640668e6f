﻿using System.Collections;
using System.Collections.Generic;

public class BitOperation 
{
    public static bool IsBitZero(ulong value, byte bitIndex)
    {
        return (value & (ulong)(1 << bitIndex)) == 0;
    }
    
    /// <summary>
    /// Set the target bit to 1 or 0
    /// </summary>
    /// <param name="setToOne">true to 1, false to 0</param>
    public static void SetBit(ulong value, byte bitIndex, bool setToOne)
    {
        if (setToOne)
        {
            value |= (ulong)1 << bitIndex;
        }
        else
        {
            value &= (ulong)~(1 << bitIndex);
        }
    }
}
