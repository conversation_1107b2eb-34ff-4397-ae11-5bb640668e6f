// Unity built-in shader source. Copyright (c) 2016 Unity Technologies. MIT license (see license.txt)

Shader "UI/Default SDF Font" {
	Properties{
		_Tex0("Font Texture 0", 2D) = "white" {}
		_Tex1("Font Texture 1", 2D) = "white" {}
		_Tex2("Font Texture 2", 2D) = "white" {}
		_Tex3("Font Texture 3", 2D) = "white" {}
		_Tex4("Font Texture 4", 2D) = "white" {}
		_Tex5("Font Texture 5", 2D) = "white" {}
		_Tex6("Font Texture 6", 2D) = "white" {}
		_Tex7("Font Texture 7", 2D) = "white" {}

		_Color("Tint", Color) = (1,1,1,1)

		// SDF Properties
		_Width("Width", Range(0, 1)) = 0.5
		_Edge("Edge", Range(0, 0.5)) = 0.1
		
		// Outline Properties
		[Toggle] _OutlineEnabled("Enable Outline", Float) = 0
		_OutlineColor("Outline Color", Color) = (0,0,0,1)
		_OutlineWidth("Outline Width", Range(0, 0.5)) = 0.1
		_OutlineEdge("Outline Edge", Range(0, 0.5)) = 0.05
		
		// Shadow Properties
		[Toggle] _ShadowEnabled("Enable Shadow", Float) = 0
		_ShadowColor("Shadow Color", Color) = (0,0,0,0.5)
		_ShadowOffsetX("Shadow Offset X", Range(-1, 1)) = -0.1
		_ShadowOffsetY("Shadow Offset Y", Range(-1, 1)) = -0.1
		_ShadowWidth("Shadow Width", Range(0, 1)) = 0.4
		_ShadowEdge("Shadow Edge", Range(0, 0.5)) = 0.1

		_StencilComp("Stencil Comparison", Float) = 8
		_Stencil("Stencil ID", Float) = 0
		_StencilOp("Stencil Operation", Float) = 0
		_StencilWriteMask("Stencil Write Mask", Float) = 255
		_StencilReadMask("Stencil Read Mask", Float) = 255

		_ColorMask("Color Mask", Float) = 15

		[Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip("Use Alpha Clip", Float) = 0
	}

		SubShader
		{
			Tags
			{
				"Queue" = "Transparent"
				"IgnoreProjector" = "True"
				"RenderType" = "Transparent"
				"PreviewType" = "Plane"
				"CanUseSpriteAtlas" = "True"
			}

			Stencil
			{
				Ref[_Stencil]
				Comp[_StencilComp]
				Pass[_StencilOp]
				ReadMask[_StencilReadMask]
				WriteMask[_StencilWriteMask]
			}

			Cull Off
			Lighting Off
			ZWrite Off
			ZTest[unity_GUIZTestMode]
			Blend SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
			ColorMask[_ColorMask]

			Pass
			{
				Name "Default"
				Tags { "LightMode" = "ForwardBase" }
			CGPROGRAM
				#pragma vertex vert
				#pragma fragment frag
				#pragma target 2.0

				#include "UnityCG.cginc"
				#include "UnityUI.cginc"

				#pragma multi_compile __ UNITY_UI_ALPHACLIP SOFT_CLIP 
				#pragma shader_feature _ _OUTLINEENABLED_ON
				#pragma shader_feature _ _SHADOWENABLED_ON

				struct appdata_t
				{
					float4 vertex   : POSITION;
					float4 color    : COLOR;
					float2 texcoord : TEXCOORD0;
					float2 texcoord1 : TEXCOORD1;
					UNITY_VERTEX_INPUT_INSTANCE_ID
				};

				struct v2f
				{
					float4 vertex   : SV_POSITION;
					float4 color : COLOR;
					float2 texcoord  : TEXCOORD0;
					float4 worldPosition : TEXCOORD1;
					float2 texcoord1 : TEXCOORD2;
					UNITY_VERTEX_OUTPUT_STEREO
				};

				float4 _Color;
				float4 _TextureSampleAdd;
				float4 _ClipRect;

				// SDF Properties
				float _Width;
				float _Edge;
				
				// Outline Properties
				float4 _OutlineColor;
				float _OutlineWidth;
				float _OutlineEdge;
				
				// Shadow Properties
				float4 _ShadowColor;
				float _ShadowOffsetX;
				float _ShadowOffsetY;
				float _ShadowWidth;
				float _ShadowEdge;

				#ifdef SOFT_CLIP
				half4 _BorderBlend;
				float4 _BorderBlendAlpha;
				#endif

				v2f vert(appdata_t IN)
				{
					v2f OUT;
					UNITY_SETUP_INSTANCE_ID(IN);
					UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
					OUT.worldPosition = IN.vertex;
					OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);

					OUT.texcoord = IN.texcoord;
					OUT.texcoord1 = IN.texcoord1;

					OUT.color = IN.color * _Color;
					return OUT;
				}

				sampler2D _Tex0;
				sampler2D _Tex1;
				sampler2D _Tex2;
				sampler2D _Tex3;
				sampler2D _Tex4;
				sampler2D _Tex5;
				sampler2D _Tex6;
				sampler2D _Tex7;

				float4 _Tex0_TexelSize;
				float4 _Tex1_TexelSize;
				float4 _Tex2_TexelSize;
				float4 _Tex3_TexelSize;
				float4 _Tex4_TexelSize;
				float4 _Tex5_TexelSize;
				float4 _Tex6_TexelSize;
				float4 _Tex7_TexelSize;

				inline float GetSoft2DClipping(in float2 position, in float4 clipRect , in half4 borderBlend)
				{
					float2 inside = smoothstep(clipRect.xy , clipRect.xy + borderBlend.xy, position.xy) * smoothstep(clipRect.zw , clipRect.zw - borderBlend.zw, position.xy);
					return inside.x * inside.y;
				}

				// Sample the correct texture based on texcoord1.x
				float SampleDistance(float2 uv, float textureIndex)
				{
					float distance = 0.5;
					
					if (textureIndex == 0)
						distance = tex2D(_Tex0, uv).a;
					else if (textureIndex == 1)
						distance = tex2D(_Tex1, uv).a;
					else if (textureIndex == 2)
						distance = tex2D(_Tex2, uv).a;
					else if (textureIndex == 3)
						distance = tex2D(_Tex3, uv).a;
					else if (textureIndex == 4)
						distance = tex2D(_Tex4, uv).a;
					else if (textureIndex == 5)
						distance = tex2D(_Tex5, uv).a;
					else if (textureIndex == 6)
						distance = tex2D(_Tex6, uv).a;
					else if (textureIndex == 7)
						distance = tex2D(_Tex7, uv).a;
					
					return distance;
				}

				// Get texel size for the current texture
				float2 GetTexelSize(float textureIndex)
				{
					float2 texelSize = float2(1.0/512.0, 1.0/512.0); // Default fallback
					
					if (textureIndex == 0)
						texelSize = _Tex0_TexelSize.xy;
					else if (textureIndex == 1)
						texelSize = _Tex1_TexelSize.xy;
					else if (textureIndex == 2)
						texelSize = _Tex2_TexelSize.xy;
					else if (textureIndex == 3)
						texelSize = _Tex3_TexelSize.xy;
					else if (textureIndex == 4)
						texelSize = _Tex4_TexelSize.xy;
					else if (textureIndex == 5)
						texelSize = _Tex5_TexelSize.xy;
					else if (textureIndex == 6)
						texelSize = _Tex6_TexelSize.xy;
					else if (textureIndex == 7)
						texelSize = _Tex7_TexelSize.xy;
					
					return texelSize;
				}

				fixed4 frag(v2f IN) : SV_Target
				{
					float textureIndex = IN.texcoord1.x;
					float distance = SampleDistance(IN.texcoord.xy, textureIndex);

					// Convert distance to signed distance (TMP-style)
					// In SDF: 0.5 is the edge, <0.5 is inside, >0.5 is outside
					float sd = (_Width - distance); // Positive = inside, negative = outside

					// Calculate derivatives for anti-aliasing
					float2 duv = float2(ddx(IN.texcoord.x), ddy(IN.texcoord.y));
					float pixelDist = length(duv) * 0.707;
					float scale = 1.0 / pixelDist; // Scale for anti-aliasing

					// Start with transparent background
					float4 finalColor = float4(0, 0, 0, 0);

					#ifdef _SHADOWENABLED_ON
					// Shadow with texture-size relative offset
					float2 texelSize = GetTexelSize(textureIndex);
					float2 shadowOffset = float2(_ShadowOffsetX, _ShadowOffsetY) * texelSize * 10.0;
					float2 shadowUV = IN.texcoord.xy + shadowOffset;
					float shadowDistance = SampleDistance(shadowUV, textureIndex);
					float shadowSd = (_ShadowWidth - shadowDistance);
					float shadowAlpha = saturate(shadowSd * scale + 0.5);

					// Add shadow as base layer
					float4 shadowColor = float4(_ShadowColor.rgb, shadowAlpha * _ShadowColor.a);
					finalColor = shadowColor;
					#endif

					#ifdef _OUTLINEENABLED_ON
					// TMP-style outline calculation
					float outline = _OutlineWidth * scale;
					float softness = _OutlineEdge * scale;

					// Face alpha (main text)
					float faceAlpha = saturate((sd - outline * 0.5 + softness * 0.5) / max(softness, 0.001));

					// Outline alpha (TMP formula)
					float outlineAlpha = saturate((sd + outline * 0.5) / max(softness, 0.001)) * sqrt(min(1.0, outline / scale));

					// Blend outline over shadow
					float4 outlineColor = float4(_OutlineColor.rgb, outlineAlpha * _OutlineColor.a);
					finalColor.rgb = lerp(finalColor.rgb, outlineColor.rgb, outlineColor.a);
					finalColor.a = max(finalColor.a, outlineColor.a);

					// Apply face color over outline
					float4 faceColor = float4(IN.color.rgb, faceAlpha * IN.color.a);
					finalColor.rgb = lerp(finalColor.rgb, faceColor.rgb, faceColor.a);
					finalColor.a = max(finalColor.a, faceColor.a);
					#else
					// No outline - just main text
					float faceAlpha = saturate(sd * scale + 0.5);
					finalColor = float4(IN.color.rgb, faceAlpha * IN.color.a);
					#endif

					float4 color = finalColor;

					#ifdef SOFT_CLIP
					color.a *= GetSoft2DClipping(IN.worldPosition.xy, _ClipRect, _BorderBlend);
					#endif

					#ifdef UNITY_UI_ALPHACLIP
					clip(color.a - 0.001);
					#endif

					return color;
				}
			ENDCG
			}
		}
}
