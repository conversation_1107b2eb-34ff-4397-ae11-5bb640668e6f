﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.InteropServices;
using UnityEngine;
using UnityEngine.UI;

//[ExtensionOfNativeClass]
public class UniTextBridge
{
    public enum Flags : byte
    {
        TextDirty = 0,
        FontDirty = 1,
        Max = 63,
    }

    public bool isTextDirty
    {
        get { return !BitOperation.IsBitZero(m_BitFlags, (byte)Flags.TextDirty); }
        set { BitOperation.SetBit(m_BitFlags, (byte)Flags.TextDirty, value); }
    }

    public bool isFontDirty
    {
        get { return !BitOperation.IsBitZero(m_BitFlags, (byte)Flags.FontDirty); }
        set { BitOperation.SetBit(m_BitFlags, (byte)Flags.FontDirty, value); }
    }

    [SerializeField]
    [HideInInspector]
    protected ulong m_BitFlags = 0;

    private string m_Text;
    public string text
    {
        get { return m_Text; }
        set
        {
            if (m_Text != value)
            {
                m_Text = value;
                UniTextWrapper.SetText(m_NativePtr, m_Text);
                isTextDirty = true;
            }
        }
    }

    public int fontSize
    {
        get
        {
            return UniTextWrapper.GetFontSize(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetFontSize(m_NativePtr, value);
        }
    }

    public HorizontalAlignment horizontalAlignment
    {
        get
        {
            return (HorizontalAlignment)UniTextWrapper.GetHorizontalAlignment(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetHorizontalAlignment(m_NativePtr, (byte)value);
        }
    }

    public VerticalAlignment verticalAlignment
    {
        get
        {
            return (VerticalAlignment)UniTextWrapper.GetVerticalAlignment(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetVerticalAlignment(m_NativePtr, (byte)value);
        }
    }

    public HorizontalOverflow horizontalOverflow
    {
        get
        {
            return (HorizontalOverflow)UniTextWrapper.GetHorizontalOverflow(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetHorizontalOverflow(m_NativePtr, (byte)value);
        }
    }

    public VerticalOverflow verticalOverflow
    {
        get
        {
            return (VerticalOverflow)UniTextWrapper.GetVerticalOverflow(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetVerticalOverflow(m_NativePtr, (byte)value);
        }
    }

    public float lineSpacing
    {
        get
        {
            return UniTextWrapper.GetLineSpacing(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetLineSpacing(m_NativePtr, value);
        }
    }

    public float characterSpacing
    {
        get
        {
            return UniTextWrapper.GetCharacterSpacing(m_NativePtr);
        }
        set
        {
            UniTextWrapper.SetCharacterSpacing(m_NativePtr, value);
        }
    }

    public bool enableRichText
    {
        get
        {
            return UniTextWrapper.HasRichText(m_NativePtr);
        }
        set
        {
            UniTextWrapper.EnableRichText(m_NativePtr, value);
        }
    }

    public bool enableKerning
    {
        get
        {
            return UniTextWrapper.HasKerning(m_NativePtr);
        }
        set
        {
            UniTextWrapper.EnableKerning(m_NativePtr, value);
        }
    }

    public bool enableAutoSize
    {
        get
        {
            return UniTextWrapper.HasAutoSize(m_NativePtr);
        }
        set
        {
            UniTextWrapper.EnableAutoSize(m_NativePtr, value);
        }
    }

    public bool enableRTL
    {
        get
        {
            return UniTextWrapper.HasRTL(m_NativePtr);
        }
        set
        {
            UniTextWrapper.EnableRTL(m_NativePtr, value);
        }
    }

    public UniFont font
    {
        set
        {
            if (value != null)
                UniTextWrapper.SetFont(m_NativePtr, value.GetNativePtr());
        }
    }

    /*
    public string fontPath
    {
        set
        {
            UniTextWrapper.SetFont(m_NativePtr, value);
        }
    }
    */

    private IntPtr m_NativePtr;
    public UniTextBridge()
    {
        m_NativePtr = UniTextWrapper.CreateUniText();
    }

    ~UniTextBridge() 
    {
        UniTextWrapper.DestroyUniText(m_NativePtr);
    }

    public void OnEnable()
    {
        UniTextWrapper.SetActive(m_NativePtr, true);
    }

    public void OnDisable()
    {
        UniTextWrapper.SetActive(m_NativePtr, false);
    }

    public void SetPivot(float x, float y)
    {
        UniTextWrapper.SetPivot(m_NativePtr, x, y);
    }

    public void SetExtents(float width, float height)
    {
        UniTextWrapper.SetExtents(m_NativePtr, width, height);
    }

    const int kShareVertexBufferSize = 256;
    //static UniVertex2D[] vertices = new UniVertex2D[kShareVertexBufferSize];
    //readonly UIVertex[] m_TempVerts = new UIVertex[4];
    static Vector2[] positions = new Vector2[kShareVertexBufferSize];
    static Vector4[] uvs = new Vector4[kShareVertexBufferSize];
    static Color32[] colors = new Color32[kShareVertexBufferSize];

    public unsafe void OnUpdateText(VertexHelper toFill)
    {
        toFill.Clear();

        UnityEngine.Profiling.Profiler.BeginSample("Native Rebuild");
        UniTextWrapper.Rebuild(m_NativePtr);
        UnityEngine.Profiling.Profiler.EndSample();

        int vertexCount = UniTextWrapper.GetVertexCount(m_NativePtr);
        //Debug.LogFormat("OnUpdateText: {0} to Ptr {1}, and vertCount : {2}", m_Text, m_NativePtr, vertexCount);

        if (vertexCount > 0)
        {
            UnityEngine.Profiling.Profiler.BeginSample("Fill Mesh");
            int offset = 0;
            while (offset < vertexCount)
            {
                int step = Mathf.Min(vertexCount - offset, kShareVertexBufferSize);

                // Benchmark 1:
                //UniVertex2D* vertices = stackalloc UniVertex2D[vertexCount];
                /*
                UniTextWrapper.FillVertices(m_NativePtr, vertices, offset, step);
                for (int i = 0; i < step; i++)
                {
                    int tempVertsIndex = i & 3;
                    m_TempVerts[tempVertsIndex].position = vertices[i].position;
                    m_TempVerts[tempVertsIndex].uv0 = vertices[i].uv0;
                    m_TempVerts[tempVertsIndex].color = vertices[i].color;
                    if (tempVertsIndex == 3)
                        toFill.AddUIVertexQuad(m_TempVerts);
                }
                */

                // Benchmark 2: data oriented copy
                UniTextWrapper.FillVerticesPerChannel(m_NativePtr, positions, uvs, colors, offset, step);
                int startIndex = toFill.currentVertCount;
                for (int i = 0; i < step; i++)
                {
                    //for (int i = 0; i < 4; i++)
                    //    AddVert(verts[i].position, verts[i].color, verts[i].uv0, verts[i].uv1, verts[i].normal, verts[i].tangent);

                    //int tempVertsIndex = i & 3;
                    //m_TempVerts[tempVertsIndex].position = positions[i];
                    //m_TempVerts[tempVertsIndex].uv0 = uvs[i];
                    //m_TempVerts[tempVertsIndex].color = colors[i];
                    //if (tempVertsIndex == 3)
                    //    toFill.AddUIVertexQuad(m_TempVerts);

                    toFill.AddVert(positions[i], colors[i], uvs[i], new Vector2(uvs[i].z, uvs[i].w), Vector3.zero, Vector4.zero); 
                    
                    int tempVertsIndex = i & 3; 
                    if (tempVertsIndex == 3)
                    {
                        toFill.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
                        toFill.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
                        startIndex += 4;
                    }
                }


                offset += step;
            }
            UnityEngine.Profiling.Profiler.EndSample();
        }

    }
}
