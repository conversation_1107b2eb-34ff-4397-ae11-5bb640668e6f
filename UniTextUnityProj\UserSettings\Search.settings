trackSelection = true
refreshSearchWindowsInPlayMode = false
pickerAdvancedUI = false
fetchPreview = true
defaultFlags = 0
keepOpen = true
queryFolder = "Assets"
onBoardingDoNotAskAgain = true
showPackageIndexes = false
showStatusBar = false
scopes = {
}
providers = {
	asset = {
		active = true
		priority = 25
		defaultAction = null
	}
	scene = {
		active = true
		priority = 50
		defaultAction = null
	}
	adb = {
		active = false
		priority = 2500
		defaultAction = null
	}
	find = {
		active = true
		priority = 25
		defaultAction = null
	}
	packages = {
		active = false
		priority = 90
		defaultAction = null
	}
	store = {
		active = false
		priority = 100
		defaultAction = null
	}
	performance = {
		active = false
		priority = 100
		defaultAction = null
	}
	profilermarkers = {
		active = false
		priority = 100
		defaultAction = null
	}
	log = {
		active = false
		priority = 210
		defaultAction = null
	}
}
objectSelectors = {
}
recentSearches = [
]
searchItemFavorites = [
]
savedSearchesSortOrder = 0
showSavedSearchPanel = false
hideTabs = false
expandedQueries = [
]
queryBuilder = true
ignoredProperties = "id;name;classname;imagecontentshash"
helperWidgetCurrentArea = "all"
disabledIndexers = ""
minIndexVariations = 2
findProviderIndexHelper = true