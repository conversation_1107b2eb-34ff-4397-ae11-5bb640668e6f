using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;
using System.Diagnostics;

public class BenchMark_03 : MonoBehaviour
{
    public TextAsset inputTextAsset;
    public TextMeshProUGUI tmpText;

    // Start is called before the first frame update
    void Start()
    {
        // preparing
        string inputText = inputTextAsset.text;
        tmpText.font.ClearFontAssetData();

        Stopwatch sw = new Stopwatch();
        sw.Start();
        tmpText.text = inputText;
        Canvas.ForceUpdateCanvases();
        sw.Stop();
        UnityEngine.Debug.LogFormat("Benchmark_03 - TextMeshPro Text.setTextAndRebuild : time elapsed = {0}ms", sw.ElapsedMilliseconds);
    }
}

