{"Version": 1, "WorkspaceRootPath": "E:\\trunk_base\\Young\\UniTextGit\\UniTextUnityProj\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{47D65FE5-77FE-0A19-5FCF-E48605BB12B4}|Assembly-CSharp.csproj|e:\\trunk_base\\young\\unitextgit\\unitextunityproj\\assets\\unitext\\runtime\\unitextbridge.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47D65FE5-77FE-0A19-5FCF-E48605BB12B4}|Assembly-CSharp.csproj|solutionrelative:assets\\unitext\\runtime\\unitextbridge.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{904ea1b4-c6a7-4eca-94a1-76eab06c5187}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "UniTextBridge.cs", "DocumentMoniker": "E:\\trunk_base\\Young\\UniTextGit\\UniTextUnityProj\\Assets\\UniText\\RunTime\\UniTextBridge.cs", "RelativeDocumentMoniker": "Assets\\UniText\\RunTime\\UniTextBridge.cs", "ToolTip": "E:\\trunk_base\\Young\\UniTextGit\\UniTextUnityProj\\Assets\\UniText\\RunTime\\UniTextBridge.cs", "RelativeToolTip": "Assets\\UniText\\RunTime\\UniTextBridge.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAP0AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T08:26:49.516Z", "EditorCaption": ""}]}]}]}